pub mod static_vector2;
pub mod vector2;

pub use static_vector2::StaticVector2;
pub use vector2::Vector2;

pub struct Math {}

impl Math {
    pub const TAU: f32 = std::f32::consts::TAU;
    pub const PI: f32 = std::f32::consts::PI;
    pub const PI_HALF: f32 = std::f32::consts::FRAC_PI_2;
    pub const INF: f32 = std::f32::INFINITY;
    pub const NEG_INF: f32 = std::f32::NEG_INFINITY;
    pub const NAN: f32 = std::f32::NAN;

    pub const fn inverse_sqrt(x: f32) -> f32 {
        let i = x.to_bits();
        let y = f32::from_bits(1597100000u32 - (i >> 1));
        let x_half = f32::from_bits(i - 0x800000);

        y * (1.5f32 - x_half * y * y)
    }

    pub const fn sqrt(x: f32) -> f32 {
        x * Math::inverse_sqrt(x)
    }

    pub const fn atan(x: f32) -> f32 {
        // Use the identity atan(x) = (π/2) - atan(1/x) for |x| > 1
        if x.abs() > 1.0f32 {
            return Math::PI_HALF - Math::atan(1.0f32 / x);
        }

        // Using only one term for proper accuracy (in range of literals)
        x - x * x * x * 0.3333333f32
    }

    pub const fn atan2(y: f32, x: f32) -> f32 {
        let quotient = y / x;

        // Use atan for the angle
        match x > 0.0 {
            true => Math::atan(quotient),
            false => match y >= 0.0f32 {
                true => Math::atan(quotient) + Math::PI,
                false => Math::atan(quotient) - Math::PI,
            },
        }
    }

    pub const fn abs(x: f32) -> f32 {
        f32::from_bits(x.to_bits() & 0x7FFFFFFF)
    }

    pub const fn ceil(x: f32) -> f32 {
        0.0
    }

    pub const fn floor(x: f32) -> f32 {
        0.0
    }

    pub const fn round(x: f32) -> f32 {
        0.0
    }

    pub const fn increment(x: f32, size: usize) -> f32 {
        0.0
    }
}
