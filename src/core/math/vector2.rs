pub struct Vector2 {
    x: f32,
    y: f32,
}

impl Vector2 {
    pub const fn new() -> Vector2 {
        Vector2 { x: 0.0, y: 0.0 }
    }

    pub const fn new_custom(x: f32, y: f32) -> Vector2 {
        Vector2 { x, y }
    }

    pub const fn new_both(value: f32) -> Vector2 {
        Vector2 { x: value, y: value }
    }

    pub const fn x(&self) -> f32 {
        self.x
    }

    pub const fn y(&self) -> f32 {
        self.y
    }

    pub const fn neg_x(&self) -> f32 {
        -self.x
    }

    pub const fn neg_y(&self) -> f32 {
        -self.y
    }

    pub const fn to_tuple(&self) -> (f32, f32) {
        (self.x(), self.y())
    }

    pub const fn to_array(&self) -> [f32; 2usize] {
        [self.x(), self.y()]
    }

    pub const fn length_squared(&self) -> f32 {
        self.x() * self.x() + self.y() * self.y()
    }

    pub const fn length(&self) -> f32 {
        self.length_squared()
    }
}
