use criterion::{black_box, criterion_group, criterion_main, Criterion};
use verturion::core::Math;

/// Benchmark Math methods vs standard library equivalents
fn math_vs_std(c: &mut Criterion) {
    let test_value = 42.0f32;
    let test_value2 = 5.7f32;

    // Square root comparisons
    c.bench_function("std_sqrt", |b| b.iter(|| black_box(test_value).sqrt()));

    c.bench_function("Math_sqrt", |b| {
        b.iter(|| Math::sqrt(black_box(test_value)))
    });

    // Inverse square root comparisons
    c.bench_function("std_inverse_sqrt", |b| {
        b.iter(|| 1.0 / black_box(test_value).sqrt())
    });

    c.bench_function("Math_inverse_sqrt", |b| {
        b.iter(|| Math::inverse_sqrt(black_box(test_value)))
    });

    // Atan comparisons
    c.bench_function("std_atan", |b| b.iter(|| black_box(test_value).atan()));

    c.bench_function("Math_atan", |b| {
        b.iter(|| Math::atan(black_box(test_value)))
    });

    // Atan2 comparisons
    c.bench_function("std_atan2", |b| {
        b.iter(|| black_box(test_value).atan2(black_box(test_value2)))
    });

    c.bench_function("Math_atan2", |b| {
        b.iter(|| Math::atan2(black_box(test_value), black_box(test_value2)))
    });

    // Abs comparisons
    c.bench_function("std_abs", |b| b.iter(|| black_box(test_value).abs()));

    c.bench_function("Math_abs", |b| b.iter(|| Math::abs(black_box(test_value))));
}

criterion_group!(benches, math_vs_std,);
criterion_main!(benches);
